Date : 2025-05-25 21:09:48
Directory : c:\Users\<USER>\VSCodeProjects\doctest
Total : 10 files,  430 codes, 87 comments, 138 blanks, all 655 lines

Languages
+----------+------------+------------+------------+------------+------------+
| language | files      | code       | comment    | blank      | total      |
+----------+------------+------------+------------+------------+------------+
| Python   |         10 |        430 |         87 |        138 |        655 |
+----------+------------+------------+------------+------------+------------+

Directories
+------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                               | files      | code       | comment    | blank      | total      |
+------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                  |         10 |        430 |         87 |        138 |        655 |
| SearchContent                                                                      |         10 |        430 |         87 |        138 |        655 |
+------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+
| filename                                                                           | language | code       | comment    | blank      | total      |
+------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\add_comment.py                | Python   |         38 |         11 |         24 |         73 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\add_comment_by_index.py       | Python   |         20 |          9 |          8 |         37 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\add_comments.py               | Python   |         34 |         11 |         22 |         67 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\build_tree_with_slibing.py    | Python   |        118 |         22 |         30 |        170 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\fill_tree_with_page.py        | Python   |         48 |          1 |          8 |         57 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\find_text_in_pdf.py           | Python   |         39 |          7 |         10 |         56 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\get_nodes_by_level.py         | Python   |         49 |         18 |         16 |         83 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\main_add_comment_with_tree.py | Python   |         37 |          3 |         10 |         50 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\titlegenerator.py             | Python   |         35 |          5 |          9 |         49 |
| c:\Users\<USER>\VSCodeProjects\doctest\SearchContent\treenode.py                   | Python   |         12 |          0 |          1 |         13 |
| Total                                                                              |          |        430 |         87 |        138 |        655 |
+------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+