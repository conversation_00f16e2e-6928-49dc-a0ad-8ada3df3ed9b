import zipfile
import os
import re
import shutil
import xml.etree.ElementTree as ET
from docx import Document

def extract_table_content(docx_path, output_dir="extracted_content"):
    """
    从Word文档的表格中提取文字和图片，支持多种图片格式（包括WMF）
    
    参数:
    docx_path: Word文档路径
    output_dir: 输出目录
    
    返回:
    list: 包含表格数据的列表，每个表格包含行、单元格、文本和图片信息
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    img_dir = os.path.join(output_dir, "images")
    os.makedirs(img_dir, exist_ok=True)
    
    # 解压整个docx文件
    with zipfile.ZipFile(docx_path) as docx_zip:
        docx_zip.extractall(output_dir)
    
    # 获取文档关系映射
    rels_path = os.path.join(output_dir, "word", "_rels", "document.xml.rels")
    image_rels = {}
    if os.path.exists(rels_path):
        tree = ET.parse(rels_path)
        root = tree.getroot()
        # 定义命名空间
        ns = {'rel': 'http://schemas.openxmlformats.org/package/2006/relationships'}
        for rel in root.findall('rel:Relationship', ns):
            r_id = rel.get('Id')
            target = rel.get('Target')
            rel_type = rel.get('Type')
            # 检查是否是图片关系
            if rel_type and 'image' in rel_type:
                image_rels[r_id] = target
    
    # 处理文档
    doc = Document(docx_path)
    table_data = []
    
    # 支持的图片扩展名列表
    supported_image_exts = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.wmf', '.emf', '.tiff', '.svg')
    
    # 处理表格
    for table_idx, table in enumerate(doc.tables):
        table_content = []
        for row_idx, row in enumerate(table.rows):
            row_content = []
            for cell_idx, cell in enumerate(row.cells):
                # 提取文字
                text = "\n".join([para.text for para in cell.paragraphs])
                
                # 提取图片
                cell_images = []
                
                # 获取单元格中的所有图片关系
                for rel_id in get_image_relationships(cell):
                    if rel_id in image_rels:
                        img_path = image_rels[rel_id]
                        # 处理相对路径
                        if img_path.startswith('../'):
                            img_path = img_path[3:]
                        src_path = os.path.join(output_dir, "word", img_path)
                        
                        # 检查文件扩展名是否支持
                        _, ext = os.path.splitext(src_path)
                        if ext.lower() in supported_image_exts:
                            if os.path.exists(src_path):
                                # 创建唯一文件名
                                img_name = os.path.basename(img_path)
                                dest_name = f"table{table_idx}_row{row_idx}_cell{cell_idx}_{img_name}"
                                dest_path = os.path.join(img_dir, dest_name)
                                
                                # 复制图片
                                shutil.copy2(src_path, dest_path)
                                cell_images.append(os.path.abspath(dest_path))
                
                row_content.append({
                    "text": text,
                    "images": cell_images
                })
            table_content.append(row_content)
        
        table_data.append({
            "table_index": table_idx,
            "content": table_content
        })
    
    # 清理临时解压文件
    #cleanup_temp_files(output_dir)
    
    return table_data

def get_image_relationships(cell):
    """从单元格中提取所有图片的关系ID"""
    image_rels = []
    cell_xml = cell._element.xml
    
    # 使用正则表达式查找所有图片关系
    # 匹配格式为 r:embed="rIdX"
    pattern = r'r:embed="(rId\d+)"'
    matches = re.findall(pattern, cell_xml)
    
    # 添加找到的关系ID
    for match in matches:
        if match not in image_rels:
            image_rels.append(match)
    
    return image_rels

def cleanup_temp_files(output_dir):
    """清理临时解压文件"""
    # 删除解压的Word目录
    word_dir = os.path.join(output_dir, "word")
    if os.path.exists(word_dir):
        shutil.rmtree(word_dir, ignore_errors=True)
    
    # 删除其他临时文件
    temp_files = [
        "[Content_Types].xml",
        "_rels",
        "docProps"
    ]
    
    for f in temp_files:
        path = os.path.join(output_dir, f)
        if os.path.exists(path):
            if os.path.isdir(path):
                shutil.rmtree(path, ignore_errors=True)
            else:
                os.remove(path)

# 使用示例
if __name__ == "__main__":
    # 替换为您的Word文档路径
    docx_path = "input/sample5.docx"
    
    # 提取表格内容
    result = extract_table_content(docx_path, "output")
    
    # 打印结果
    print("=" * 80)
    print("表格内容提取结果")
    print("=" * 80)
    
    total_images = 0
    for table in result:
        print(f"\n表格 #{table['table_index']}:")
        for row_idx, row in enumerate(table['content']):
            print(f"  行 #{row_idx}:")
            for cell_idx, cell in enumerate(row):
                print(f"    单元格 #{cell_idx}:")
                print(f"      文本: {cell['text']}")
                if cell['images']:
                    print("      图片:")
                    for img_path in cell['images']:
                        print(f"        {os.path.basename(img_path)}")
                        total_images += 1
                else:
                    print("      无图片")
    
    print("\n" + "=" * 80)
    print(f"处理完成! 共提取 {len(result)} 个表格，{total_images} 张图片")
    print(f"所有图片保存在: {os.path.abspath('extracted_output/images')}")
    print("支持的图片格式: PNG, JPG, JPEG, GIF, BMP, WMF, EMF, TIFF, SVG")
    print("=" * 80)