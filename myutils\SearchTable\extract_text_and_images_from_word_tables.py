from docx import Document
from docx.oxml import parse_xml
import os
import os.path

def extract_text_and_images_from_word_tables(doc_path, output_dir="extracted_images"):
    """
    提取Word文档表格中的文本和图片，并建立对应关系
    
    参数:
        doc_path (str): Word文档路径
        output_dir (str): 图片输出目录
        
    返回:
        dict: 包含表格索引、行索引、单元格索引对应的文本和图片列表的字典
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载Word文档
    doc = Document(doc_path)
    
    # 结果字典：{表格索引: [[{单元格数据}]]}
    result = {}
    
    # 遍历所有表格
    for table_idx, table in enumerate(doc.tables):
        table_data = []
        
        # 遍历表格行
        for row_idx, row in enumerate(table.rows):
            row_data = []
            
            # 遍历行中的单元格
            for cell_idx, cell in enumerate(row.cells):
                # 提取单元格文本
                cell_text = cell.text.strip()
                
                # 提取单元格中的图片
                cell_images = []
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        # 查找图片元素
                        drawing = run._element.find(".//w:drawing", namespaces=run._element.nsmap)
                        if drawing is not None:
                            # 获取图片的嵌入关系ID
                            blip = drawing.find(".//a:blip", namespaces={
                                "a": "http://schemas.openxmlformats.org/drawingml/2006/main"
                            })
                            if blip is not None:
                                rId = blip.get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed")
                                if rId and rId in doc.part.related_parts:
                                    # 获取图片数据
                                    image_part = doc.part.related_parts[rId]
                                    image_data = image_part.blob
                                    
                                    # 生成唯一文件名并保存图片
                                    img_ext = image_part.content_type.split("/")[-1]
                                    if img_ext == "png":
                                        ext = "png"
                                    elif img_ext == "jpeg":
                                        ext = "jpg"
                                    elif img_ext == "emf":
                                        print("正在检查EMF格式...")
                                    else:
                                        ext = img_ext
                                        
                                    image_name = f"table_{table_idx}_row_{row_idx}_cell_{cell_idx}_{len(cell_images)}.{ext}"
                                    image_path = os.path.join(output_dir, image_name)
                                    
                                    with open(image_path, "wb") as f:
                                        f.write(image_data)
                                    
                                    cell_images.append(image_path)
                
                # 添加到行数据
                row_data.append({
                    "text": cell_text,
                    "images": cell_images
                })
            
            # 添加到表格数据
            table_data.append(row_data)
        
        # 添加到最终结果
        result[table_idx] = table_data
    
    return result

if __name__ == "__main__":
    # 使用示例
    doc_path = "input/sample5.docx"  # 替换为你的Word文档路径
    output_dir = "extracted_images"  # 图片输出目录
    
    result = extract_text_and_images_from_word_tables(doc_path, output_dir)
    
    # 打印结果
    print("="*60)
    print(f"提取结果: {len(result)} 个表格")
    print("="*60)
    
    for table_idx, table_data in result.items():
        print(f"\n表格 {table_idx} ({len(table_data)} 行):")
        for row_idx, row_data in enumerate(table_data):
            for cell_idx, cell_data in enumerate(row_data):
                if cell_data["images"]:
                    print(f"  行 {row_idx} 列 {cell_idx}:")
                    #print(f"    文本: '{cell_data['text']}'")
                    if cell_data["images"]:
                        print(f"    图片: {len(cell_data['images'])} 张")
                        for i, img_path in enumerate(cell_data["images"]):
                            print(f"      {i+1}. {os.path.basename(img_path)}")
                    else:
                        print("    图片: 无")
                    print("-" * 40)