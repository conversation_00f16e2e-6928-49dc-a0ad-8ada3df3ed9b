# Diff Details

Date : 2025-05-29 21:50:59

Directory c:\\Users\\<USER>\\VSCodeProjects\\doctest

Total : 9 files,  110 codes, 19 comments, 32 blanks, all 161 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [SearchContent/add\_comment.py](/SearchContent/add_comment.py) | Python | 4 | 0 | 0 | 4 |
| [SearchContent/add\_comment\_by\_index.py](/SearchContent/add_comment_by_index.py) | Python | 1 | -1 | 0 | 0 |
| [SearchContent/add\_comments.py](/SearchContent/add_comments.py) | Python | 33 | 9 | 15 | 57 |
| [SearchContent/build\_tree\_with\_slibing.py](/SearchContent/build_tree_with_slibing.py) | Python | 7 | 0 | 1 | 8 |
| [SearchContent/convert2txt.py](/SearchContent/convert2txt.py) | Python | 21 | 3 | 6 | 30 |
| [SearchContent/fill\_tree\_with\_page.py](/SearchContent/fill_tree_with_page.py) | Python | 11 | 2 | 1 | 14 |
| [SearchContent/find\_text\_in\_pdf.py](/SearchContent/find_text_in_pdf.py) | Python | 1 | 1 | 0 | 2 |
| [SearchContent/main\_add\_comment\_with\_tree.py](/SearchContent/main_add_comment_with_tree.py) | Python | 31 | 5 | 9 | 45 |
| [SearchContent/treenode.py](/SearchContent/treenode.py) | Python | 1 | 0 | 0 | 1 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details