from collections import deque
from treenode import TreeNode

'''
class TreeNode:
    def __init__(self, num, text, level):
        self.num = num      # 节点编号（如"1.1"）
        self.text = text    # 节点文本
        self.level = level  # 节点层级
        self.parent = None  # 父节点
        self.children = []  # 子节点列表
        self.right_sibling = None  # 右兄弟节点
'''

def get_nodes_by_level(root, target_level):
    """
    获取树中指定层级的所有节点
    
    参数:
        root: 树的根节点
        target_level: 目标层级（从1开始）
    
    返回:
        list: 包含目标层级所有节点的列表
    """
    if not root or target_level < 1:
        return []
    
    result = []
    queue = deque([root])
    
    while queue:
        node = queue.popleft()
        
        # 如果当前节点层级等于目标层级，加入结果
        if node.level == target_level:
            result.append(node)
        
        # 如果当前节点层级小于目标层级，继续处理子节点
        elif node.level < target_level:
            for child in node.children:
                queue.append(child)
    
    result.reverse()
    
    return result

# 示例用法
if __name__ == "__main__":
    # 构建示例树
    root = TreeNode("", "ROOT", 0)
    
    # 第一层节点
    n1 = TreeNode("1", "项目概述", 1)
    n2 = TreeNode("2", "实施方案", 1)
    n3 = TreeNode("3", "项目总结", 1)
    root.children = [n1, n2, n3]
    
    # 第二层节点
    n1_1 = TreeNode("1.1", "项目背景", 2)
    n1_2 = TreeNode("1.2", "项目意义", 2)
    n1.children = [n1_1, n1_2]
    
    n2_1 = TreeNode("2.1", "第一阶段", 2)
    n2_2 = TreeNode("2.2", "第二阶段", 2)
    n2.children = [n2_1, n2_2]
    
    # 第三层节点
    n1_1_1 = TreeNode("1.1.1", "技术背景", 3)
    n1_1.children = [n1_1_1]
    
    # 测试函数
    print("层级1节点:")
    for node in get_nodes_by_level(root, 1):
        print(f"{node.num} {node.text}")
    
    print("\n层级2节点:")
    for node in get_nodes_by_level(root, 2):
        print(f"{node.num} {node.text}")
    
    print("\n层级3节点:")
    for node in get_nodes_by_level(root, 3):
        print(f"{node.num} {node.text}")