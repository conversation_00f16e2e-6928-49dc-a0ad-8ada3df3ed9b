{"file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/treenode.py": {"language": "Python", "code": 13, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/titlegenerator.py": {"language": "Python", "code": 35, "comment": 5, "blank": 9}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/main_add_comment_with_tree.py": {"language": "Python", "code": 68, "comment": 8, "blank": 19}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/find_text_in_pdf.py": {"language": "Python", "code": 40, "comment": 8, "blank": 10}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/fill_tree_with_page.py": {"language": "Python", "code": 59, "comment": 3, "blank": 9}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/convert2txt.py": {"language": "Python", "code": 21, "comment": 3, "blank": 6}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/build_tree_with_slibing.py": {"language": "Python", "code": 125, "comment": 22, "blank": 31}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/get_nodes_by_level.py": {"language": "Python", "code": 49, "comment": 18, "blank": 16}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comment_by_index.py": {"language": "Python", "code": 21, "comment": 8, "blank": 8}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comments.py": {"language": "Python", "code": 67, "comment": 20, "blank": 37}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comment.py": {"language": "Python", "code": 42, "comment": 11, "blank": 24}}