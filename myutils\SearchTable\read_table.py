from docx import Document

def find_all_tables(docpath):

# 打开Word文档
    doc = Document(docpath)

    '''
    # 遍历文档中的所有表格
    for table in doc.tables:
        # 遍历表格中的所有行
        for row in table.rows:
            # 遍历行中的所有单元格
            for cell in row.cells:
                # 逐行打印单元格的文本内容
                print(cell.text)
            print("------ 行结束 ------\n")  # 表示一行的结束
    '''

    # 如果需要，可以提取整个表格为列表的列表
    tables_data = [] #将最后的保存结果存入到列表中
    for table in doc.tables:
        table_data = []
        for row in table.rows:
            row_data = [cell.text for cell in row.cells]
            table_data.append(row_data)
        tables_data.append(table_data)

    # 打印提取的表格数据
    for table in tables_data:
        print(table)

def find_table_by_name(file_name, find_text):
    #ele = []
    doc_obj = Document(file_name)
    paras = doc_obj.paragraphs
    all_tables = doc_obj.tables
    find_text = find_text.encode('utf-8')
    find_text = find_text.decode('utf-8')
    res = []
    for para in paras:
        #if ara.text == find_text:
        if para.text.find(find_text) != -1:
            print("found!")
            ele =para._p.getnext()
            while ele.tag[-3:] != 'tbl' and ele.tag != '':
                ele = ele.getnext()
                if ele.tag != '':
                    for table in all_tables:
                        if table._tbl == ele:
                            for i in range(len(table.rows)):
                                lin = []
                                for j in range(len(table.columns)):
                                    lin.append(table.cell(i, j).text)
                                res.append(lin)
    return res

	



if __name__ == '__main__':
    find_all_tables('input/sample5.docx')

    