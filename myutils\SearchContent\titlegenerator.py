class TitleGenerator:
    def __init__(self, base_title="环境评价"):
        self.base_title = base_title
        self.chinese_nums = ["一", "二", "三", "四", "五","六", "七", "八", "九", "十"]
        self.roman_nums = ["I", "II", "III", "IV", "V"]
    
    def generate_arabic_titles(self, levels=3):
        """生成阿拉伯数字编号标题"""
        titles = []
        for i in range(1,10):
            titles.append(f"{i}. {self.base_title}")  # 一级
            
            if levels >= 2:
                for j in range(1,10):
                    titles.append(f"{i}.{j} {self.base_title}")  # 二级
                    if levels >= 3:
                        for k in range(1,10):
                            titles.append(f"{i}.{j}.{k} {self.base_title}")  # 三级
        return titles
        
    def generate_chinese_titles(self):
        """生成中文数字编号标题"""
        titles=[]
        for i in range(0,10):
            titles.append(f"{self.chinese_nums[i]}. {self.base_title}")  # 一级
            titles.append(f"{self.chinese_nums[i]}、{self.base_title}")  # 一级
           
        return titles
    
    def generate_roman_titles(self):
        """生成罗马数字编号标题"""
        return [f"{self.roman_nums[0]}. {self.base_title}"]
    
    def generate_all_titles(self):
        """生成所有类型的标题"""
        titles = []
        titles.extend(self.generate_arabic_titles(levels=3))
        titles.extend(self.generate_chinese_titles())
        titles.extend(self.generate_roman_titles())
        return titles

# 使用示例
if __name__ == "__main__":
    generator = TitleGenerator("建设项目基本情况")
    all_titles = generator.generate_all_titles()

    print("生成的多级标题：")
    print(all_titles)
