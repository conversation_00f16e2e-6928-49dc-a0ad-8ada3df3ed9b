from docx import Document
from docx2python import docx2python
from PIL import Image
import re
import os

def extract_wmf_images_and_text(docx_path, output_dir):
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 使用docx2python提取图片和文本关系
    docx2py = docx2python(docx_path)
    images = docx2py.images  # 获取所有图片数据（字典格式：{图片路径: 二进制数据}）
    text_relations = []  # 存储文本与图片的关联关系
    
    # 处理docx2python提取的图片
    for img_path, img_data in images.items():
        if img_path.endswith('.wmf'):
            # 保存WMF文件
            wmf_path = os.path.join(output_dir, img_path)
            with open(wmf_path, 'wb') as f:
                f.write(img_data)
            
            # 转换为PNG（提高分辨率）
            png_path = wmf_path.replace('.wmf', '.png')
            with Image.open(wmf_path) as img:
                img.load(dpi=300)  # 高分辨率加载
                img.save(png_path)
            os.remove(wmf_path)  # 删除临时WMF文件
        if img_path.endswith('.emf'):
            # 保存WMF文件
            wmf_path = os.path.join(output_dir, img_path)
            with open(wmf_path, 'wb') as f:
                f.write(img_data)
            
            # 转换为PNG（提高分辨率）
            png_path = wmf_path.replace('.emf', '.png')
            with Image.open(wmf_path) as img:
                img.load(dpi=300)  # 高分辨率加载
                img.save(png_path)
            os.remove(wmf_path)  # 删除临时WMF文件
        if img_path.endswith('.png'):
            # 保存PNG文件
            png_path = os.path.join(output_dir, img_path)
            with open(png_path, 'wb') as f:
                f.write(img_data)
    
    # 使用python-docx遍历表格，建立文本与图片关系
    doc = Document(docx_path)
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                # 检查单元格内是否存在图片（通过XML解析）
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        # 匹配图片的rId（如 rId5）
                        match = re.search(r'rId(\d+)', run._element.xml)
                        if match:
                            rId = match.group(0)
                            # 关联逻辑：记录表格位置、单元格文本、图片路径
                            for img_path in images:
                                if rId in img_path:  # 根据rId匹配图片
                                    png_path = os.path.join(output_dir, img_path.replace('.wmf', '.png').replace('.emf', '.png'))
                                    relation = {
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "text": cell_text,
                                        "image_path": png_path
                                    }
                                    text_relations.append(relation)
    docx2py.close()
    return text_relations

# 使用示例
relations = extract_wmf_images_and_text("input/sample5.docx", "output_images")
for rel in relations:
    print(f"表格 {rel['table_index']} 的单元格 ({rel['row']}, {rel['cell']}):")
    print(f"  文本: {rel['text']}")
    print(f"  图片路径: {rel['image_path']}\n")