import sys
import os
# 获取当前文件所在目录的父目录（DOCTEST目录）
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)  # 添加到Python路径

from base import WordProcessorPlugin
from typing import List

from myutils.SearchTable.main_extract_emf_images import add_comment_on_matrial

class MaterialProcessor(WordProcessorPlugin):
    def __init__(self, table_name:str,table_columns:List[str],graph_name:str) -> None:
        super().__init__()
        self.table_name = table_name
        self.table_columns = table_columns  # 字符串列表类型
        self.graph_name = graph_name

    def process(self, doc_bytes: bytes) -> bytes:
        bytes = add_comment_on_matrial(doc_bytes,self.table_name,self.table_columns,self.graph_name)
        return bytes