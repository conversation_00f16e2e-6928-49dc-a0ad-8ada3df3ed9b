from spire.doc import Document, CommentMark, CommentMarkType
def add_comment_by_index(doc,sectionindex,paraindex,comment_content):  
# 创建Document对象

    print(f"正在添加注释{comment_content}到第{sectionindex}节的第{paraindex}个段落。")
    # 获取文档第一个节
    section = doc.Sections.get_Item(sectionindex)
    # 获取节中第一个段落
    paragraph = section.Paragraphs.get_Item(paraindex)

    # 添加一个批注到段落
    comment = paragraph.AppendComment(comment_content)

    # 设置批注作者
    comment.Format.Author = "Sara"

    # 创建批注起始标记和结束标记，并设置为新建批注的开始和结束标记
    commentStart = CommentMark(doc, CommentMarkType.CommentStart)
    commentEnd = CommentMark(doc, CommentMarkType.CommentEnd)
    commentStart.CommentId = comment.Format.CommentId
    commentEnd.CommentId = comment.Format.CommentId

    # 将批注起始标记和结束标记分别插入段落开头和结束位置
    paragraph.ChildObjects.Insert(0, commentStart)
    paragraph.ChildObjects.Add(commentEnd)

    '''
    # 保存文档
    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    doc.SaveToFile("output/comment-"+current_time+".docx")
    doc.Close()
    '''

if __name__ == "__main__":
    add_comment_by_index("input/sample.docx",0,1)
