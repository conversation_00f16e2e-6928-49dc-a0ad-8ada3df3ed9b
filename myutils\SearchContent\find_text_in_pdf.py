import os
from docx import Document
from PyPDF2 import PdfReader
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import tempfile
from docx2pdf import convert  # 需要额外安装
from titlegenerator import TitleGenerator

def word_to_pdf(word_path, pdf_path):
    """将Word文档转换为PDF"""
    # 使用docx2pdf库进行转换
    convert(word_path, pdf_path)

def find_text_in_pdf(pdf_path, search_text,bool):
    """在PDF中查找文本并返回页码(从0开始)"""
    reader = PdfReader(pdf_path)
    occurrences = []
    
    for page_num in range(len(reader.pages)):
        page = reader.pages[page_num]
        text = page.extract_text().replace('\t', '').replace(' ','').replace('\n','')  # 去除换行符
        if bool:
            #if text and search_text in text:
             if text and text.count(search_text) > 0:
                for _ in range(text.count(search_text)):  # 查找所有匹配项
                  occurrences.append(page_num + 1)  # 返回人类可读的页码(从1开始)
        else:
            generator = TitleGenerator(search_text)
            all_titles = generator.generate_all_titles()
            for title in all_titles:
                if text and title in text:
                    occurrences.append(page_num + 1)  # 返回人类可读的页码(从1开始)
    
    return occurrences if occurrences else None

def main():
    # 输入文件路径
    word_file = "input/sample.docx"  # 替换为你的Word文件路径
    pdf_file = "input/sample.pdf"   # 输出的PDF文件路径
    
    # 要搜索的文本
    search_text = "开场白"  # 替换为你要查找的内容
    
    # 1. 将Word转换为PDF
    word_to_pdf(word_file, pdf_file)
    print(f"Word文档已转换为PDF: {pdf_file}")
    
    # 2. 在PDF中搜索特定文本
    page_numbers = find_text_in_pdf(pdf_file, search_text,True)
    
    if page_numbers:
        print(f"找到文本 '{search_text}' 在以下页码: {', '.join(map(str, page_numbers))}")
    else:
        print(f"未找到文本 '{search_text}'")

if __name__ == "__main__":
    main()