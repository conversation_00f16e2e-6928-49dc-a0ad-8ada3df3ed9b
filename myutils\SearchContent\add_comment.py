#将第一个文本加批注，并将水印替换为空
from spire.doc import Document, Comment, CommentMark, CommentMarkType
from docx import Document as docx_document

def add_single_comment(doc,text="开场白",comment_content=None):  

    print(f"正在给 {text} 添加批注:{comment_content}")



    # 查找需要添加批注的文本
    text = doc.FindString(text, True, True)



    # 创建批注对象，并设置批注的作者和内容
    comment = Comment(doc)
    if comment_content is None:
        comment.Body.AddParagraph().Text = "Source for the 60% statistic? Is this global or region-specific?"
    else:
        comment.Body.AddParagraph().Text = comment_content
    comment.Format.Author = "Sara"
    
    if text is None:
        print(f"未找到文本 {text}")
        return
    
    # 将找到的文本获取为文本区域，并获取该文本区域所在的段落
    textRange = text.GetAsOneRange()
    paragraph =  textRange.OwnerParagraph

    # 将批注对象插入到段落中
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, comment)

    # 创建批注开始和结束标记，并将其设置为新建批注的开始和结束标记
    commentStart = CommentMark(doc, CommentMarkType.CommentStart)
    commentEnd = CommentMark(doc, CommentMarkType.CommentEnd)
    commentStart.CommentId = comment.Format.CommentId
    commentEnd.CommentId = comment.Format.CommentId

    # 将批注开始和结束标记分别插入到文本区域的前面和后面
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange), commentStart)
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, commentEnd)

  

        # 保存文档



    

def load_document(file_path):
    return docx_document(file_path)
def save_document(doc, file_path):
    doc.save(file_path)
def search_and_replace(olddoc, old_text, new_text):
    doc=load_document(olddoc)
    for para in doc.paragraphs:
        if old_text in para.text:
            para.text = para.text.replace(old_text, new_text)
    save_document(doc,olddoc)


if __name__ == "__main__":
      # 创建Document对象
    docpath="input/sample.docx"
    doc = Document()
    # 载入Word文档
    doc.LoadFromFile(docpath)
    add_single_comment(doc,"开场白")
    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    doc.SaveToFile("output/comment-"+current_time+".docx")
    doc.Close()
