# Details

Date : 2025-05-25 21:09:48

Directory c:\\Users\\<USER>\\VSCodeProjects\\doctest

Total : 10 files,  430 codes, 87 comments, 138 blanks, all 655 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [SearchContent/add\_comment.py](/SearchContent/add_comment.py) | Python | 38 | 11 | 24 | 73 |
| [SearchContent/add\_comment\_by\_index.py](/SearchContent/add_comment_by_index.py) | Python | 20 | 9 | 8 | 37 |
| [SearchContent/add\_comments.py](/SearchContent/add_comments.py) | Python | 34 | 11 | 22 | 67 |
| [SearchContent/build\_tree\_with\_slibing.py](/SearchContent/build_tree_with_slibing.py) | Python | 118 | 22 | 30 | 170 |
| [SearchContent/fill\_tree\_with\_page.py](/SearchContent/fill_tree_with_page.py) | Python | 48 | 1 | 8 | 57 |
| [SearchContent/find\_text\_in\_pdf.py](/SearchContent/find_text_in_pdf.py) | Python | 39 | 7 | 10 | 56 |
| [SearchContent/get\_nodes\_by\_level.py](/SearchContent/get_nodes_by_level.py) | Python | 49 | 18 | 16 | 83 |
| [SearchContent/main\_add\_comment\_with\_tree.py](/SearchContent/main_add_comment_with_tree.py) | Python | 37 | 3 | 10 | 50 |
| [SearchContent/titlegenerator.py](/SearchContent/titlegenerator.py) | Python | 35 | 5 | 9 | 49 |
| [SearchContent/treenode.py](/SearchContent/treenode.py) | Python | 12 | 0 | 1 | 13 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)