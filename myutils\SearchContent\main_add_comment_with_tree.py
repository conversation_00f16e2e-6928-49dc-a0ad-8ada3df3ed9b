from build_tree_with_slibing import complete_right_siblings, build_directory_tree, parse_txt_headings,print_tree_with_siblings
from fill_tree_with_page import fill_tree_with_page
from find_text_in_pdf import find_text_in_pdf,word_to_pdf
from add_comment import add_comment
from spire.doc import Document, Comment, CommentMark, CommentMarkType
from convert2txt import convert_docx_to_txt
from docx import Document as docx_document
from add_comments import add_comment_with_index
from add_comment_by_index import add_comment_by_index

def visit(doc,node):
    #print(f"{node.num} {node.text}:{node.page}")
    if node.page==-1:
        if node.level==1:
            add_comment_by_index(doc,0,0,node.text+" 未找到"+"\n缺失内容为第一级标题，故添加到报告首页")
            #print(f"{node.num} {node.text}:{node.page} 未找到 添加注释")
            return True
        if node.parent.level!=0 and node.parent.page!=-1:
            add_comment_with_index(doc,node.parent.cleantext,node.parent.validindex,node.text+" 未找到")
            #print(f"{node.num} {node.text}:{node.page} 未找到 添加注释")
            return True
    else:
        return False


def visit_tree_with_siblings(doc,node, indent=0):
    """打印带右邻居信息的树结构"""
    prefix = '    ' * (indent-1) + '├── ' if indent > 0 else ''
    sibling_info = f" → {node.right_sibling.num}" if node.right_sibling else ""
    visit(doc,node)
    for child in node.children:
        visit_tree_with_siblings(doc,child, indent + 1)


def load_document(file_path):
    return docx_document(file_path)
def save_document(doc, file_path):
    doc.save(file_path)
def search_and_replace(olddoc, old_text, new_text):
    doc=load_document(olddoc)
    for para in doc.paragraphs:
        if old_text in para.text:
            para.text = para.text.replace(old_text, new_text)
    save_document(doc,olddoc)
def handle_watermarker(docpath):
    search_and_replace(docpath,"Evaluation Warning: The document was created with Spire.Doc for Python.","")

def add_comment_with_tree(docpath1,docpath2):
    convert_docx_to_txt(docpath1)

    txtpath1=docpath1.split(".")[0]+".txt"

    entries = parse_txt_headings(txtpath1)
    tree = build_directory_tree(entries)
    tree = complete_right_siblings(tree)
    
    filepath2=docpath2.split(".")[0]
    pdf_file = filepath2+".pdf"   # 输出的PDF文件路径
    word_file = docpath2   # 替换为你的Word文件路径
    word_to_pdf(word_file, pdf_file)
    print(f"Word文档已转换为PDF: {pdf_file}")

    fill_tree_with_page(tree,1,filepath2)
    fill_tree_with_page(tree,2,filepath2)
    fill_tree_with_page(tree,3,filepath2)

    print("目录树结构（→表示右邻居）：")
    for child in tree.children:
        print_tree_with_siblings(child)
    

    #docpath="input/sample42.docx"
    doc = Document()
    # 载入Word文档
    doc.LoadFromFile(docpath2)    

    #print("目录树结构（→表示右邻居）：")
    for child in tree.children:
        visit_tree_with_siblings(doc,child)

    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    output_path = "output/comment-"+current_time+".docx"
    doc.SaveToFile(output_path)
    doc.Close()

    handle_watermarker(output_path)

if __name__ == "__main__":
  # 构建示例树
    docpath1="input/sample41.docx"
    docpath2="input/sample42.docx"
    add_comment_with_tree(docpath1,docpath2)

    