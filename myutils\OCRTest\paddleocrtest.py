from paddleocr import PaddleOCR

def ocr_cpu(image_path):
    ocr = PaddleOCR(use_angle_cls=True, lang="ch",det_db_thresh=0.2, det_db_box_thresh=0.4)  # need to run only once to download and load model into memory
    #img_path = 'OCRTest/image1.png'
    import cv2
    img = cv2.imread(image_path)
    padded_img = cv2.copyMakeBorder(img, 50, 50, 50, 50, cv2.BORDER_CONSTANT, value=[255,255,255])
    result = ocr.ocr(padded_img)

    return result[0]['rec_texts']

def ocr_gpu(img_path):
    from paddleocr import PaddleOCR
    import os
    os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'  # 解决部分Windows库冲突[2](@ref)

    # 初始化OCR引擎（启用GPU）
    ocr = PaddleOCR(
        use_angle_cls=True,  # 开启文本方向分类
        lang='ch',           # 中文识别（en:英文, fr:法语等）
        #use_gpu=True,        # 启用GPU加速
        #gpu_mem=4000         # 显存限制（单位MB）
        #use_gpu=True,
        enable_mkldnn=False,  # 禁用MKL-DNN优化
        #enable_tensorrt=False  # 禁用TensorRT
    )

    # 执行OCR识别
    #img_path = 'test.jpg'
    result = ocr.ocr(img_path, cls=True)  # cls=True启用方向分类

    print(result)
    
    textlist=[]

    # 解析结果
    for line in result:
        for word in line:
            coords = word[0]                # 文本位置坐标[[x1,y1],...,[x4,y4]]
            text = word[1][0]                # 识别文本
            confidence = word[1][1]          # 置信度
            print(f"坐标: {coords} | 文本: {text} | 置信度: {confidence:.2f}")

            textlist.append(text)
    
    return textlist

if __name__ == "__main__":
    image_path = 'myutils/OCRTest/image2.png'  # 替换为您的图片路径
    #image_path = 'output_emf_images\\table1_row0_cell1_0.png'  # 替换为您的图片路径
   
    text = ocr_cpu(image_path)
    print(text)
