from docx import Document
from spire.doc import Document as spire_doc
#from spire.doc.common import FileFormat
import docx2txt

def convert_docx_to_txt(docx_file):
    # 打开Word文档
    doc = Document(docx_file)
    # 创建TXT文件并写入Word文档内容
    filename = docx_file.split(".")[0]
    txt_path=filename+".txt"
    with open(txt_path, 'w', encoding='utf-8') as txt_file:
        for paragraph in doc.paragraphs:
            txt_file.write(paragraph.text + '\n')
    print(f"已将 {docx_file} 转换为 {txt_path}")



def convert_docx_to_txt3(fileName):
    text = docx2txt.process(fileName)
    filename = docx_file.split(".")[0]
    txt_file=filename+".txt"
    outFile = open(txt_file, "w", encoding='utf-8')
    outFile.write(text)
    outFile.close()

if __name__ == "__main__":
    docx_file = "input/sample41.docx"  # 替换为你的Word文档路径
    convert_docx_to_txt(docx_file)
