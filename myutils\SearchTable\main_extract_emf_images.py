from docx import Document
from PIL import Image
#import pyemf
import re
import os
import json
from io import BytesIO

def extract_emf_images(doc_bytes, output_dir):
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    try:
        doc_stream = BytesIO(doc_bytes)  # 字节流转换为内存流
        doc = Document(doc_stream)  # 加载为Document对象
    except Exception as e:
        raise ValueError("文件格式错误或非.docx文件，请检查输入是否为有效的.docx字节流") from e
    relations = []
    
    # 正则匹配图片ID
    pattern = re.compile(r'rId(\d+)')
    
    # 遍历表格
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            
            for cell_idx, cell in enumerate(row.cells):
                
                ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
                text_parts = []
                textboxes = cell._element.findall('.//w:txbxContent', ns)
                for textbox in textboxes:
                    
                    # 提取文本框内所有段落文本
                    for paragraph in textbox.findall('.//w:p', ns):
                        # 拼接段落内所有文本片段
                        para_text = ''.join(run.text for run in paragraph.findall('.//w:t', ns))
                        text_parts.append(para_text)

                count=0
                cell_text = cell.text.strip()
                
                # 检查单元格内图片
                for paragraph in cell.paragraphs:
                   
                    for run in paragraph.runs:
                        # 通过XML解析定位EMF图片
                        match = pattern.search(run._element.xml)
                        if match:
                            rId = f'rId{match.group(1)}'
                            
                            # 提取图片二进制数据
                            rel = doc.part.rels[rId]
                            if "image" in rel.target_ref:
                                if rel.target_ref.endswith('.emf'):
                                    # EMF图片处理
                                    emf_data = rel.target_part.blob
                                    
                                    # 保存原始EMF文件
                                    emf_path = os.path.join(output_dir, f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.emf')
                                    count += 1
                                    with open(emf_path, 'wb') as f:
                                        f.write(emf_data)
                                    
                                    # 转换为PNG
                                    png_path = emf_path.replace('.emf', '.png')
                                    convert_emf_to_png(emf_data, output_dir, emf_path)
                                    
                                    # 建立关系映射
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": emf_path,
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                    })
                                    
                                elif rel.target_ref.endswith('.png'):
                                    # PNG图片处理
                                    png_data = rel.target_part.blob
                                    
                                    # 直接保存PNG文件
                                    png_path = os.path.join(output_dir, f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.png')
                                    count += 1
                                    with open(png_path, 'wb') as f:
                                        f.write(png_data)
                                    
                                    # 建立关系映射
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": None,  # 没有对应的EMF文件
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                    })
    # 保存关系数据
    with open(os.path.join(output_dir, 'relations.json'), 'w') as f:
        json.dump(relations, f, indent=2)
    
    return relations


import os
import tempfile
from PIL import Image

def convert_emf_to_highres_png(img_data, output_dir, filename, dpi=300):
    """
    将EMF/WMF格式图像数据转换为高分辨率PNG
    
    参数:
        img_data (bytes): 二进制图像数据 (EMF/WMF格式)
        output_dir (str): 输出目录路径
        filename_base (str): 输出文件名（不含扩展名）
        dpi (int): 输出分辨率（默认300 DPI）
    
    返回:
        str: 生成的PNG文件路径
    
    异常:
        ValueError: 输入数据为空时抛出
        IOError: 图像处理失败时抛出
    """
    # 验证输入数据
    if not img_data:
        raise ValueError("输入图像数据为空")
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建临时文件路径（使用安全随机文件名）
    with tempfile.NamedTemporaryFile(suffix='.emf', delete=False, dir=output_dir) as temp_file:
        temp_path = temp_file.name
    
    try:
        # 写入临时EMF文件
        with open(temp_path, 'wb') as f:
            f.write(img_data)
        
        filename_base = os.path.splitext(filename)[0]
        # 设置输出PNG路径
        png_path =  f"{filename_base}.png"
        
        # 转换并保存为高分辨率PNG
        with Image.open(temp_path) as img:
            # 高分辨率加载（支持矢量图元文件）
            img.load(dpi=dpi)
            
            # 保存为PNG格式（保留元数据）
            img.save(png_path, 'PNG', dpi=(dpi, dpi))
        
        return png_path
    
    except Exception as e:
        # 清理临时文件（如果转换失败）
        if os.path.exists(temp_path):
            os.remove(temp_path)
        raise IOError(f"图像转换失败: {str(e)}")
    
    finally:
        # 确保删除临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)

import os
from io import BytesIO
from PIL import Image

def convert_emf_to_png(img_data, output_dir, filename, dpi=1000):
    """
    将EMF图像数据转换为高分辨率PNG文件
    
    参数:
        img_data: EMF图像的二进制数据
        output_dir: PNG文件的输出目录
        filename: 输出文件名（不含扩展名）
        dpi: 输出分辨率（默认300 DPI）
    
    返回:
        输出PNG文件的完整路径
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 从二进制数据创建图像对象
    emf_image = Image.open(BytesIO(img_data))
    
    # 修正非均匀DPI导致的宽高比失真[8](@ref)
    if "dpi" in emf_image.info and emf_image.info["dpi"][0] != emf_image.info["dpi"][1]:
        x_dpi, y_dpi = emf_image.info["dpi"]
        aspect_ratio = x_dpi / y_dpi
        new_size = (emf_image.width, int(emf_image.height * aspect_ratio))
        emf_image = emf_image.resize(new_size, Image.LANCZOS)
    
    # 创建白色背景并合并图像（解决透明背景问题）[1,2](@ref)
    if emf_image.mode in ('RGBA', 'LA'):
        bg = Image.new("RGB", emf_image.size, (255, 255, 255))
        bg.paste(emf_image, mask=emf_image.split()[3])
        processed_image = bg
    else:
        processed_image = emf_image
    
    # 构建输出路径
    png_filename = f"{os.path.splitext(filename)[0]}.png"
    output_path = png_filename
    
    # 保存为高分辨率PNG
    processed_image.save(output_path, format="PNG", dpi=(dpi, dpi))
    
    return output_path

def convert_emf_to_png2(img_data, output_dir, filename, dpi=300):
    """
    优化版EMF转PNG函数，通过物理尺寸计算和高质量重采样提升清晰度
    
    参数:
        img_data: EMF图像的二进制数据
        output_dir: PNG文件的输出目录
        filename: 输出文件名（不含扩展名）
        dpi: 输出分辨率（默认300 DPI）
    
    返回:
        输出PNG文件的完整路径
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 从二进制数据加载EMF并自动关闭资源
    with Image.open(BytesIO(img_data)) as emf_image:
        # 关键改进1：基于物理尺寸计算目标像素尺寸（保留矢量精度）
        if "dpi" in emf_image.info:
            x_dpi, y_dpi = emf_image.info["dpi"]
            # 计算物理尺寸（英寸）
            phys_width_inch = emf_image.width / x_dpi
            phys_height_inch = emf_image.height / y_dpi
            # 按目标DPI转换物理尺寸为像素[6,8](@ref)
            target_width = int(phys_width_inch * dpi)
            target_height = int(phys_height_inch * dpi)
        else:
            # 若无DPI元数据，则按比例缩放至目标DPI基准
            scale_factor = dpi / 72  # 假设默认72dpi
            target_width = int(emf_image.width * scale_factor)
            target_height = int(emf_image.height * scale_factor)
        
        # 关键改进2：使用LANCZOS重采样（抗锯齿保真）[7,8](@ref)
        resized_img = emf_image.resize(
            (target_width, target_height), 
            resample=Image.LANCZOS
        )
        
        # 关键改进3：RGBA模式下的白色背景合成（避免透明失真）
        if resized_img.mode == 'RGBA':
            bg = Image.new("RGB", resized_img.size, (255, 255, 255))
            bg.paste(resized_img, mask=resized_img.split()[3])
            processed_image = bg
        else:
            processed_image = resized_img.convert("RGB")
        
        # 构建输出路径并保存
        png_path = f"{os.path.splitext(filename)[0]}.png"
        processed_image.save(png_path, "PNG", dpi=(dpi, dpi), compress_level=9)
        
    return png_path

def add_comment_on_matrial(doc_bytes, material_table_name="主要原辅料情况表"):

    # 使用示例
    relations = extract_emf_images(doc_bytes, "output_emf_images")
    for rel in relations:
        print(f"表格{rel['table_index']}的单元格({rel['row']},{rel['cell']})")
        print(f"  文本: {rel['cell_text']}")
        print(f"  EMF路径: {rel['emf_path']}")
        print(f"  PNG路径: {rel['png_path']}\n")
        print(f"  文本框片段: {rel['text_parts']}\n")

    def search_content_in_list(list, content):
        for item in list:
            if content in item:
                return True
        return False
    
    def search_content_in_text(text,content):
        if content in text:
            return True
        return False
    
    import sys
    import os
    # 获取当前文件所在目录的父目录（DOCTEST目录）
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.append(project_root)  # 添加到Python路径
    from SearchTable.read_nested_table import extract_tables
    from SearchTable.read_nested_table import extract_process_materail_relations

    extracted_data = extract_tables(doc_bytes) 

    for i, table in enumerate(extracted_data):
        print(f"表格 {i+1}:")
        for j,row in enumerate(table):
            if j!=0:
                print(row)
        print("-" * 40)

    process_materail_relations=extract_process_materail_relations(extracted_data[0])
    print("工艺与原料关系：",process_materail_relations)

    main_process,_=process_materail_relations[0]
    graph_name=main_process+"生产工艺流程"

    related_imgs=[]
    search_content=graph_name
    for rel in relations:
        if search_content_in_list( rel['text_parts'],search_content) or search_content_in_list( rel['cell_text'],search_content):
            related_imgs.append(rel['png_path'])
        if search_content_in_text( rel['text_parts'],search_content) or search_content_in_text( rel['cell_text'],search_content):
            related_imgs.append(rel['png_path'])
    print(related_imgs)

    import sys
    import os
    # 获取当前文件所在目录的父目录（DOCTEST目录）
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.append(project_root)  # 添加到Python路径

    from OCRTest.paddleocrtest import ocr_cpu,ocr_gpu

    related_img_texts=[]
    for related_img in related_imgs:
        texts=ocr_cpu(related_img)
        #texts=ocr_gpu(related_img)
        #print(text)

        #for text in texts:
           # if text!='':
                #related_img_texts.append(text)
        if search_content_in_list(texts,"原料") or search_content_in_list(texts,"原材料"):
            related_img_texts.append(texts)
    print(related_img_texts)

    related_texts=[]
    search_content=graph_name
    for rel in relations:
        if search_content_in_list( rel['text_parts'],search_content) or search_content_in_list( rel['cell_text'],search_content):
            related_texts.append(rel['cell_text'])
        if search_content_in_text( rel['text_parts'],search_content) or search_content_in_text( rel['cell_text'],search_content):
            related_texts.append(rel['cell_text'])
    related_texts=list(set(related_texts))
    print(related_texts)

    related_texts_content=""
    for related_text in related_texts:
        related_texts_content=related_texts_content+related_text


    text_split_keywords=[]
    for process_material_relation in process_materail_relations:
        process,materials=process_material_relation
        text_split_keywords.append(process+"生产工艺流程")
    print("分段关键词为：",text_split_keywords)

    realted_text_segments=[];split_content=related_texts_content
    for i,keyword in enumerate(text_split_keywords):
        parts=split_content.split(keyword)        
        realted_text_segments.append(parts[-2])
        split_content=parts[-1]
        if(i==len(text_split_keywords)-1):
            realted_text_segments.append(parts[-1])
        
        
    print("分段结果:", realted_text_segments)
    for i,segment in enumerate(realted_text_segments):
        print(f"第{i}段：")
        print(segment)



    for i,(process,matierials) in enumerate(process_materail_relations):      
        matierials_related_graph=[]
        for matieral in matierials:
            if search_content_in_list(related_img_texts[i],matieral):

                matierials_related_graph.append({"name":matieral,"related":True})
            else:
                matierials_related_graph.append({"name":matieral,"related":False})

        print(i,"原料相关图片：",matierials_related_graph)

        
        

        matierials_related_text=[]
        for matieral in matierials:
            if search_content_in_text(realted_text_segments[i+1],matieral):

                matierials_related_text.append({"name":matieral,"related":True})
            else:
                matierials_related_text.append({"name":matieral,"related":False})

        print("原料相关文本：",matierials_related_text)

        comment_word_graph="表中原材料在对应图中未找到！"+"原材料缺失："
        for matieral_related in matierials_related_graph:
            if not matieral_related["related"]:
                comment_word_graph=comment_word_graph+matieral_related["name"]+" "
        print(comment_word_graph)

        comment_word_text="表中原材料在对应文本中未找到！"+"原材料缺失："
        for matieral_related in matierials_related_text:
            if not matieral_related["related"]:
                comment_word_text=comment_word_text+matieral_related["name"]+" "
        print(comment_word_text)
    
    '''
    from SearchContent.add_comment import add_single_comment
    from spire.doc import Document as SpireDoc
    from spire.doc.common import Stream
    from spire.doc import FileFormat
    
    doc = SpireDoc()
        # 载入Word文档
    doc.LoadFromStream(Stream(doc_bytes),FileFormat.Docx)
    #document.LoadFromStream(input_stream, FileFormat.Docx)
    add_single_comment(doc,material_table_name,comment_word_graph)
    add_single_comment(doc,material_table_name,comment_word_text)

    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    doc.SaveToFile("output/comment-"+current_time+".docx")

    from spire.doc import FileFormat
    from spire.doc.common import Stream
    output_stream=Stream()
    doc.SaveToStream(output_stream, FileFormat.Docx)
    word_bytes = output_stream.ToArray()
    doc.Close()
    '''

    return word_bytes

if __name__ == "__main__":
    doc_path="input/input.docx"
    with open(doc_path, "rb") as file:
        word_bytes = file.read()
    add_comment_on_matrial(word_bytes)