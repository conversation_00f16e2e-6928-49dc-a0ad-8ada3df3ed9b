from get_nodes_by_level import get_nodes_by_level
from build_tree_with_slibing import complete_right_siblings, build_directory_tree, parse_txt_headings,print_tree_with_siblings
from find_text_in_pdf import find_text_in_pdf


def fill_tree_with_page(tree, tree_level,docx_prefix_path):
  if tree_level == 1:
    nodes = get_nodes_by_level(tree, tree_level)
    for node in nodes:
      pages=find_text_in_pdf(docx_prefix_path+".pdf",node.cleantext,False)
      node.page=pages[-1] if pages else -1
  elif tree_level == 2:
    nodes = get_nodes_by_level(tree, tree_level)
    for node in nodes:
      validpages=[]

      pages=find_text_in_pdf(docx_prefix_path+".pdf",node.cleantext,True)
      validindexs=[]
      if pages is None:
        node.page=-1
      else:        
        #for page in pages:
        for index in range(len(pages)):
          page=pages[index]
          if page>=node.parent.page and node.right_sibling.page!=-1 and page<=node.right_sibling.page:
            validpages.append(page)
            validindexs.append(index)
          elif node.right_sibling.page==-1 and page>=node.parent.page:
            validpages.append(page) 
            validindexs.append(index)
      node.page=validpages[0] if validpages else -1
      node.validindex=validindexs[0] if validindexs else -1
  else:
    nodes = get_nodes_by_level(tree, tree_level)
    for node in nodes:
      validpages=[]
      validindexs=[]
      if node.parent.page!=-1:
       
        pages=find_text_in_pdf(docx_prefix_path+".pdf",node.cleantext,True)
        if pages is None:
          node.page=-1
        else:
          #for page in pages
          for index in range(len(pages)):
            page=pages[index]
            if page>=node.parent.page and node.right_sibling.page!=-1 and page<=node.right_sibling.page and node.parent.right_sibling and node.parent.right_sibling.page!=-1 and page<=node.parent.right_sibling.page:
              validpages.append(page)
              validindexs.append(index)
            elif node.right_sibling.page==-1 and page>=node.parent.page and node.parent.right_sibling and node.parent.right_sibling.page!=-1 and page<=node.parent.right_sibling.page:
              validpages.append(page)
              validindexs.append(index)
      node.page=validpages[0] if validpages else -1
      node.validindex=validindexs[0] if validindexs else -1
  
  print(f"第{tree_level}级目录树结构已填充页码")

  return tree

if __name__ == "__main__":
  # 构建示例树
    entries = parse_txt_headings("input/sample.txt")
    tree = build_directory_tree(entries)
    tree = complete_right_siblings(tree)

    fill_tree_with_page(tree,1,"input/sample42")
    fill_tree_with_page(tree,2,"input/sample42")

    print("目录树结构（→表示右邻居）：")
    for child in tree.children:
        print_tree_with_siblings(child)