# Details

Date : 2025-05-29 21:50:59

Directory c:\\Users\\<USER>\\VSCodeProjects\\doctest

Total : 11 files,  540 codes, 106 comments, 170 blanks, all 816 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [SearchContent/add\_comment.py](/SearchContent/add_comment.py) | Python | 42 | 11 | 24 | 77 |
| [SearchContent/add\_comment\_by\_index.py](/SearchContent/add_comment_by_index.py) | Python | 21 | 8 | 8 | 37 |
| [SearchContent/add\_comments.py](/SearchContent/add_comments.py) | Python | 67 | 20 | 37 | 124 |
| [SearchContent/build\_tree\_with\_slibing.py](/SearchContent/build_tree_with_slibing.py) | Python | 125 | 22 | 31 | 178 |
| [SearchContent/convert2txt.py](/SearchContent/convert2txt.py) | Python | 21 | 3 | 6 | 30 |
| [SearchContent/fill\_tree\_with\_page.py](/SearchContent/fill_tree_with_page.py) | Python | 59 | 3 | 9 | 71 |
| [SearchContent/find\_text\_in\_pdf.py](/SearchContent/find_text_in_pdf.py) | Python | 40 | 8 | 10 | 58 |
| [SearchContent/get\_nodes\_by\_level.py](/SearchContent/get_nodes_by_level.py) | Python | 49 | 18 | 16 | 83 |
| [SearchContent/main\_add\_comment\_with\_tree.py](/SearchContent/main_add_comment_with_tree.py) | Python | 68 | 8 | 19 | 95 |
| [SearchContent/titlegenerator.py](/SearchContent/titlegenerator.py) | Python | 35 | 5 | 9 | 49 |
| [SearchContent/treenode.py](/SearchContent/treenode.py) | Python | 13 | 0 | 1 | 14 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)