'''
import fitz


pdf_document = fitz.open("input/sample5.pdf")
for current_page in range(len(pdf_document)):
    for image in pdf_document.get_page_images(current_page):
        xref = image[0]
        pix = fitz.Pixmap(pdf_document, xref)
        if pix.n < 5: # this is GRAY or RGB
            pix.save("output/page%s-%s.png" % (current_page, xref))
        else: # CMYK: convert to RGB first
            pix1 = fitz.Pixmap(fitz.csRGB, pix)
            pix1.save("output/page%s-%s.png" % (current_page, xref))
            pix1 = None
        pix = None
'''
from docx2pdf import convert  # 需要额外安装


def word_to_pdf(word_path, pdf_path):
    """将Word文档转换为PDF"""
    # 使用docx2pdf库进行转换
    convert(word_path, pdf_path)

import fitz
import os

def extract_pdf_images(pdf_path, output_folder="output"):
    """
    优化版PDF图片提取函数，解决纯黑色图片问题
    """
    # 创建输出目录
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 打开PDF文件
    pdf_document = fitz.open(pdf_path)
    
    # 获取PDF文件名（不含扩展名）
    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
    
    for page_num in range(len(pdf_document)):
        # 获取页面对象
        page = pdf_document[page_num]
        
        # 获取页面中的所有图片
        image_list = page.get_images(full=True)
        
        print(f"页面 {page_num+1} 中找到 {len(image_list)} 张图片")
        
        for img_index, img in enumerate(image_list):
            # 获取图片xref
            xref = img[0]
            
            # 使用PyMuPDF内置方法提取图片（自动处理SMask和颜色空间）
            base_image = pdf_document.extract_image(xref)
            
            if base_image:
                # 获取图片扩展名和二进制数据
                img_ext = base_image["ext"]
                img_data = base_image["image"]
                
                # 生成唯一文件名
                img_name = f"{pdf_name}_page{page_num+1}_img{img_index+1}_{xref}.{img_ext}"
                print(img_name)
                img_path = os.path.join(output_folder, img_name)
                
                # 保存图片
                with open(img_path, "wb") as img_file:
                    img_file.write(img_data)
                
                print(f"已保存图片: {img_path}")
            else:
                print(f"警告: 无法提取页面 {page_num+1} 的图片 #{img_index+1} (xref={xref})")
    
    print(f"\n图片提取完成! 共处理 {len(pdf_document)} 页")
    pdf_document.close()

if __name__ == "__main__":
    '''
    # 使用示例
    from spire.doc import Document

    # 创建Document对象
    doc = Document()
    # 载入Word文档
    doc.LoadFromFile("input/sample5.docx")

    # 删除文档指定批注
    #doc.Comments.RemoveAt(0)

    # 删除文档所有批注
    doc.Comments.Clear()

    # 保存文档
    doc.SaveToFile("input/sample51.docx")
    doc.Close()

    word_to_pdf("input/sample51.docx", "input/sample5.pdf")
    '''

    pdf_file = "input/sample5.pdf"
    extract_pdf_images(pdf_file)