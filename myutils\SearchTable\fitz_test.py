import fitz  # PyMuPDF库
import os
import re

def extract_pdf_images(pdf_path, output_dir):
    """
    从PDF中提取所有图片（包括WMF矢量图）到指定目录
    :param pdf_path: PDF文件路径
    :param output_dir: 图片输出目录
    """
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    doc = fitz.open(pdf_path)
    total_images = 0
    
    print(f"处理PDF: {os.path.basename(pdf_path)}")
    print(f"总页数: {len(doc)}")
    
    # 遍历每一页
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        # 获取当前页所有图片信息
        img_list = page.get_images(full=True)
        
        # 处理当前页的每张图片
        for img_index, img_info in enumerate(img_list):
            try:
                # 提取图片元数据
                xref = img_info[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]
                
                # 特殊处理WMF矢量图
                if image_ext == "emf":
                    # 检查是否为真正的WMF格式
                    print("正在检查WMF格式...")
                
                # 生成唯一文件名（页号+序号+格式）
                filename = f"page_{page_num+1}_img_{img_index+1}.{image_ext}"
                output_path = os.path.join(output_dir, filename)
                
                # 保存图片
                with open(output_path, "wb") as f:
                    f.write(image_bytes)
                
                total_images += 1
                print(f"已保存: {filename} ({len(image_bytes)//1024} KB)")
                
            except Exception as e:
                print(f"错误：第{page_num+1}页图片{img_index+1}提取失败 - {str(e)}")
    
    doc.close()
    print(f"\n成功提取 {total_images} 张图片到目录：{os.path.abspath(output_dir)}")

# 使用示例
if __name__ == "__main__":
    pdf_path = "input/sample5.pdf"  # PDF文件路径
    output_dir = "output"           # 图片输出目录
    
    extract_pdf_images(pdf_path, output_dir)