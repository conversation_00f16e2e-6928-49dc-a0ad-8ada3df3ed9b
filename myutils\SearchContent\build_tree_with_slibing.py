import re
from collections import deque
from treenode import TreeNode


def parse_txt_headings(file_path):
    """从TXT文件解析标题结构（与之前相同）"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    entries = []
    chinese_numbers = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '十一': 11, '十二': 12, '十三': 13, '十四': 14, '十五': 15
    }
    
    current_level = 0
    counters = [0] * 4
    chinese_counter = 0

    for line in lines:
        text = line.strip().replace('\t', '').replace(' ', '')
        if not text:
            continue

        if re.match(r'^[一二三四五六七八九十]+、', text):
            level = 1
            chinese_char = re.match(r'^([一二三四五六七八九十]+)、', text).group(1)
            chinese_counter = chinese_numbers.get(chinese_char, 0)
            if chinese_counter == 0:
                continue
            counters[1] = chinese_counter
            counters[2] = counters[3] = 0
            clean_text = text.split('、', 1)[1].strip()
            standard_num = str(chinese_counter)
            
        elif re.match(r'^\d+\.\d+', text):
            num_match = re.match(r'^(\d+(?:\.\d+)*)', text)
            if not num_match:
                continue
            num_part = num_match.group(1)
            level = num_part.count('.') + 1
            if level > 3:
                continue
            if level <= current_level:
                for l in range(level+1, 4):
                    counters[l] = 0
            if level == 2:
                counters[2] += 1
                standard_num = f"{chinese_counter}.{counters[2]}"
            elif level == 3:
                counters[3] += 1
                standard_num = f"{chinese_counter}.{counters[2]}.{counters[3]}"
            clean_text = text[len(num_part):].strip()
        else:
            continue

        current_level = level
        entries.append({
            'num': standard_num,
            'text': text,
            'cleantext': clean_text,  # 新增的清理后的文本字段，用于后续处理
            'level': level
        })
    
       
    print("标题解析完成。")

    return entries

def build_directory_tree(entries):
    """构建带有右邻居关系的目录树"""
    root = TreeNode(num="", text="ROOT",cleantext="ROOT", level=0)
    node_stack = deque([root])
    last_nodes = {}  # 记录每层最后一个节点
    
    for entry in entries:
        node = TreeNode(entry['num'], entry['text'], entry['cleantext'],entry['level'])
        
        # 找到父节点
        while node_stack[-1].level >= node.level:
            node_stack.pop()
        
        # 设置父子关系
        node.parent = node_stack[-1]
        node_stack[-1].children.append(node)
        node_stack.append(node)
        
        # 设置右邻居关系
        if node.level in last_nodes:
            last_nodes[node.level].right_sibling = node
        last_nodes[node.level] = node
        
        # 清除更深层级的last_nodes记录
        for l in range(node.level+1, max(last_nodes.keys() or [0])+1):
            if l in last_nodes:
                del last_nodes[l]
    
    print("目录树构建完成。")
    return root

def print_tree_with_siblings(node, indent=0):
    """打印带右邻居信息的树结构"""
    prefix = '    ' * (indent-1) + '├── ' if indent > 0 else ''
    sibling_info = f" → {node.right_sibling.num}" if node.right_sibling else ""
    print(f"{prefix}{node.num} {node.text}:{node.page}{sibling_info}")
    for child in node.children:
        print_tree_with_siblings(child, indent + 1)

def complete_right_siblings(root):
    """
    完善树的右兄弟关系（改进版）：
    如果节点的右兄弟为None，则将其设置为后续部分中第一个出现的上一级节点
    （即第一个层级比当前节点小的节点）
    """
    # 使用DFS遍历所有节点
    stack = []
    nodes_in_order = []  # 按DFS顺序保存所有节点
    
    # 第一次遍历：收集所有节点
    stack.append(root)
    while stack:
        node = stack.pop()
        nodes_in_order.append(node)
        # 逆序压栈保证子节点按顺序处理
        for child in reversed(node.children):
            stack.append(child)
    
    # 第二次遍历：建立右兄弟关系
    for i in range(len(nodes_in_order)):
        current_node = nodes_in_order[i]
        
        # 已经设置过右兄弟则跳过
        if current_node.right_sibling is not None:
            continue
        
        # 寻找后续第一个层级 <= 当前层级的节点
        for j in range(i+1, len(nodes_in_order)):
            candidate = nodes_in_order[j]
            if candidate.level <= current_node.level:
                current_node.right_sibling = candidate
                break
    
    print("右兄弟关系已完善。")
    return root

def find_valid_right_sibling(node):
    while node.right_sibling:
        if node.right_sibling.page != -1:
            return node.right_sibling

# 示例用法
if __name__ == "__main__":
   

    # 解析和构建树
    entries = parse_txt_headings("input/sample.txt")
    tree = build_directory_tree(entries)
    tree = complete_right_siblings(tree)
    
    # 打印带右邻居信息的树结构
    print("目录树结构（→表示右邻居）：")
    for child in tree.children:
        print_tree_with_siblings(child)
    
    '''
    # 验证右邻居关系
    print("\n右邻居关系验证：")
    def traverse(node):
        if node.right_sibling:
            print(f"{node.num} {node.text} → {node.right_sibling.num}")
        else:
            print(f"{node.num} {node.text} → None")
        for child in node.children:
            traverse(child)
    traverse(tree)
    '''