{"file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/titlegenerator.py": {"language": "Python", "code": 35, "comment": 5, "blank": 9}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/treenode.py": {"language": "Python", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/get_nodes_by_level.py": {"language": "Python", "code": 49, "comment": 18, "blank": 16}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/main_add_comment_with_tree.py": {"language": "Python", "code": 37, "comment": 3, "blank": 10}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/find_text_in_pdf.py": {"language": "Python", "code": 39, "comment": 7, "blank": 10}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/fill_tree_with_page.py": {"language": "Python", "code": 48, "comment": 1, "blank": 8}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comments.py": {"language": "Python", "code": 34, "comment": 11, "blank": 22}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/build_tree_with_slibing.py": {"language": "Python", "code": 118, "comment": 22, "blank": 30}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comment_by_index.py": {"language": "Python", "code": 20, "comment": 9, "blank": 8}, "file:///c%3A/Users/<USER>/VSCodeProjects/doctest/SearchContent/add_comment.py": {"language": "Python", "code": 38, "comment": 11, "blank": 24}}