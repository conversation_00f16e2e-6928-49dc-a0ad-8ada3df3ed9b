class TreeNode:
    def __init__(self, num, text, cleantext,level):
        self.num = num              # 标题编号（如"1.1"）
        self.text = text            # 标题文本内容
        self.cleantext=cleantext
        self.level = level          # 标题层级（1-大标题，2-中标题，...）
        self.parent = None         # 父节点
        self.children = []         # 子节点列表
        self.right_sibling = None   # 右邻居节点（新增属性）
        self.page= -1      # 页面编号（新增属性）
        self.validindex=1000 # 有效页码序列号（新增属性）

    def __repr__(self):
        return f"<TreeNode {self.num} {self.text}>"