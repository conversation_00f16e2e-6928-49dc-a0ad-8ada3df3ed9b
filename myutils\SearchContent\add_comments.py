#将所有文本加批注，并将水印替换为空
from spire.doc import Document, Comment, CommentMark, CommentMarkType
from docx import Document as docx_document

def add_comments(olddoc="input/sample.docx",text="开场白",comment=None,outputdoc=None):  

    # 创建Document对象
    doc = Document()
    # 载入Word文档
    doc.LoadFromFile(olddoc)



    # 查找需要添加批注的文本
    textlist = doc.FindAllString(text, True, True)

    print(len(textlist))




   

    for text in textlist:

    # 将找到的文本获取为文本区域，并获取该文本区域所在的段落
         # 创建批注对象，并设置批注的作者和内容
        comment = Comment(doc)
        comment.Body.AddParagraph().Text = "Source for the 60% statistic? Is this global or region-specific?"
        comment.Format.Author = "Sara"

        textRange = text.GetAsOneRange()
        paragraph =  textRange.OwnerParagraph

        # 将批注对象插入到段落中
        paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, comment)

        # 创建批注开始和结束标记，并将其设置为新建批注的开始和结束标记
        commentStart = CommentMark(doc, CommentMarkType.CommentStart)
        commentEnd = CommentMark(doc, CommentMarkType.CommentEnd)
        commentStart.CommentId = comment.Format.CommentId
        commentEnd.CommentId = comment.Format.CommentId

        # 将批注开始和结束标记分别插入到文本区域的前面和后面
        paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange), commentStart)
        paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, commentEnd)



        # 保存文档
    doc.SaveToFile("output/TextComment.docx")
    doc.Close()

def add_comment_with_index(doc,text="开场白",validindex=-1,comment_content=None,outputdoc=None):
    '''
    doc = Document()
    # 载入Word文档
    doc.LoadFromFile(olddoc)
    '''
    print(f"正在给 {text} 添加批注{comment_content}")


    # 查找需要添加批注的文本
    textlist = doc.FindAllString(text, True, True)

    #print(len(textlist))



    if validindex==1000 or len(textlist)==0:
        print("未找到文本"+text)
        return 

    text=textlist[validindex]

# 将找到的文本获取为文本区域，并获取该文本区域所在的段落
        # 创建批注对象，并设置批注的作者和内容
    comment = Comment(doc)
    if comment_content is None:
        comment.Body.AddParagraph().Text = "Source for the 60% statistic? Is this global or region-specific?"
    else:
        comment.Body.AddParagraph().Text = comment_content
    #comment.Body.AddParagraph().Text = "Source for the 60% statistic? Is this global or region-specific?"
    comment.Format.Author = "Sara"

    textRange = text.GetAsOneRange()
    paragraph =  textRange.OwnerParagraph

    # 将批注对象插入到段落中
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, comment)

    # 创建批注开始和结束标记，并将其设置为新建批注的开始和结束标记
    commentStart = CommentMark(doc, CommentMarkType.CommentStart)
    commentEnd = CommentMark(doc, CommentMarkType.CommentEnd)
    commentStart.CommentId = comment.Format.CommentId
    commentEnd.CommentId = comment.Format.CommentId

    # 将批注开始和结束标记分别插入到文本区域的前面和后面
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange), commentStart)
    paragraph.ChildObjects.Insert(paragraph.ChildObjects.IndexOf(textRange) + 1, commentEnd)


    '''
    # 保存文档
    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    doc.SaveToFile("output/comment-"+current_time+".docx")
    doc.Close()
    '''

def load_document(file_path):
    return docx_document(file_path)
def save_document(doc, file_path):
    doc.save(file_path)
def search_and_replace(olddoc, old_text, new_text):
    doc=load_document(olddoc)
    for para in doc.paragraphs:
        if old_text in para.text:
            para.text = para.text.replace(old_text, new_text)
    save_document(doc,olddoc)

if __name__ == "__main__":
    add_comments()
    search_and_replace("output/TextComment.docx","Evaluation Warning: The document was created with Spire.Doc for Python.","")